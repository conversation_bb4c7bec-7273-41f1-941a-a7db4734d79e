import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/features/chat/domain/extension/ephemeral_message_extension.dart';
import 'package:flutter_audio_room/features/chat/domain/extension/message_metadata_extension.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_context_menu/flutter_context_menu.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart' as intl;
import 'package:super_context_menu/super_context_menu.dart';

/// 消息气泡组件，用于自定义消息气泡样式
class ChatBubble extends StatefulWidget {
  final Widget child;
  final types.Message message;
  final Menu menu;
  final bool nextMessageInGroup;
  final types.User currentUser;
  final Function(BuildContext, types.Message) onMessageStatusTap;

  const ChatBubble({
    super.key,
    required this.child,
    required this.message,
    required this.menu,
    required this.nextMessageInGroup,
    required this.currentUser,
    required this.onMessageStatusTap,
  });
  
  @override
  State<ChatBubble> createState() => _ChatBubbleState();
}

class _ChatBubbleState extends State<ChatBubble> {
  // 添加 GlobalKey 用于获取 Row 的位置
  final GlobalKey _key = GlobalKey();

  @override
  Widget build(BuildContext context) {
    final currentUserIsAuthor =
        widget.message.author.id == widget.currentUser.id;
    final roundBorder = widget.nextMessageInGroup == true;
    final messageBorderRadius = 16.r;
    final borderRadius = BorderRadius.only(
      bottomLeft: Radius.circular(
        currentUserIsAuthor || roundBorder ? messageBorderRadius : 0,
      ),
      bottomRight: Radius.circular(
        !currentUserIsAuthor || roundBorder ? messageBorderRadius : 0,
      ),
      topLeft: Radius.circular(messageBorderRadius),
      topRight: Radius.circular(messageBorderRadius),
    );

    // 判断是否需要背景色（媒体消息不需要额外背景）
    final needsBackground = _needsBackground(widget.message);
    final bgColor = needsBackground
        ? (currentUserIsAuthor
            ? context.theme.colorScheme.primary
            : context.theme.colorScheme.onSurface.withAlpha(20))
        : Colors.transparent;

    final entries = <ContextMenuEntry>[];
    for (final action in widget.menu.children.cast<MenuAction>()) {
      entries.add(
        MenuItem(
          label: action.title ?? '',
          onSelected: action.callback,
        ),
      );
    }

    // 计算消息气泡的最大宽度（屏幕宽度的75%）
    final screenWidth = MediaQuery.of(context).size.width;
    final maxBubbleWidth = screenWidth * 0.75;

    return Row(
      mainAxisAlignment:
          currentUserIsAuthor ? MainAxisAlignment.end : MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (currentUserIsAuthor) ...[
          _buildTimerIndicator(context, widget.message),
          10.horizontalSpace,
        ],
        ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: maxBubbleWidth,
          ),
          child: IntrinsicWidth(
            key: _key,
            child: GestureDetector(
              onTap: widget.message.status == types.Status.error
                  ? () => widget.onMessageStatusTap(context, widget.message)
                  : null,
              onLongPress: () {
                final RenderBox? renderBox =
                    _key.currentContext?.findRenderObject() as RenderBox?;
                if (renderBox != null) {
                  final position = renderBox.localToGlobal(Offset.zero);
                  final size = renderBox.size;

                  final screenHeight = MediaQuery.of(context).size.height;
                  final isNearBottom =
                      position.dy + size.height + 150 > screenHeight;
                  final menuHeight = 32 * entries.length;
                  final menuPosition = Offset(
                    currentUserIsAuthor
                        ? position.dx + size.width
                        : position.dx,
                    isNearBottom
                        ? position.dy - menuHeight - 10.h
                        : position.dy + size.height + 10.h,
                  );

                  final menu = ContextMenu(
                    entries: entries,
                    position: menuPosition,
                    padding: EdgeInsets.zero,
                    borderRadius: BorderRadius.circular(16.r),
                  );
                  menu.show(context);
                } else {
                  final menu = ContextMenu(
                    entries: entries,
                    padding: EdgeInsets.zero,
                    borderRadius: BorderRadius.circular(16.r),
                  );
                  menu.show(context);
                }
              },
              child: _buildContextMenu(
                context,
                bgColor: bgColor,
                borderRadius: borderRadius,
                child: widget.child,
                currentUserIsAuthor: currentUserIsAuthor,
              ),
            ),
          ),
        ),
        if (!currentUserIsAuthor) ...[
          10.horizontalSpace,
          _buildTimerIndicator(context, widget.message),
        ],
      ],
    );
  }

  Widget _buildContextMenu(
    BuildContext context, {
    required Color bgColor,
    required BorderRadius borderRadius,
    required Widget child,
    required bool currentUserIsAuthor,
  }) {
    final needsBackground = _needsBackground(widget.message);

    Widget childWidget;

    if (needsBackground) {
      // 文本消息：使用背景色
      childWidget = Container(
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: borderRadius,
        ),
        child: IntrinsicWidth(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                fit: FlexFit.loose,
                child: child,
              ),
              _buildEncryptionIndicator(context, currentUserIsAuthor),
              SizedBox(width: 8.w),
            ],
          ),
        ),
      );
    } else {
      // 媒体消息：使用边框，加密指示器叠加在内容上
      childWidget = Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: bgColor.withValues(alpha: 0.5),
            width: 2.0,
          ),
          borderRadius: borderRadius,
        ),
        child: Stack(
          children: [
            // 媒体内容
            ClipRRect(
              borderRadius: BorderRadius.circular(14.r), // 稍小于边框圆角
              child: child,
            ),
            // 加密指示器叠加在右下角
            Positioned(
              right: 8.w,
              bottom: 8.h,
              child: Container(
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.lock_outlined,
                  size: 12.w,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      );
    }

    if (Platform.isIOS) {
      return ContextMenuWidget(
        menuProvider: (request) => widget.menu,
        hitTestBehavior: HitTestBehavior.opaque,
        mobileMenuWidgetBuilder: DefaultMobileMenuWidgetBuilder(
          enableBackgroundBlur: false,
        ),
        child: childWidget,
      );
    }

    return childWidget;
  }

  /// 构建加密指示器
  Widget _buildEncryptionIndicator(BuildContext context, bool isCurrentUser) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        context.showOkAlertDialog(
          title: 'Message is encrypted',
          content: 'This message is encrypted.',
        );
      },
      child: Padding(
        padding: EdgeInsets.only(bottom: 10.h),
        child: Icon(
          Icons.lock_outlined,
          size: 10.w,
          color: isCurrentUser
              ? context.theme.colorScheme.onPrimary
              : context.theme.colorScheme.onSurface,
        ),
      ),
    );
  }

  /// 构建倒计时指示器
  Widget _buildTimerIndicator(BuildContext context, types.Message message) {
    if (!message.isEphemeral) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _showEphemeralInfo(context, message);
      },
      child: Padding(
        padding: EdgeInsets.only(bottom: 16.h),
        child: Icon(
          CupertinoIcons.timer_fill,
          size: 13.w,
          color: context.colorScheme.onSurface,
        ),
      ),
    );
  }

  /// 显示阅后即焚信息
  void _showEphemeralInfo(BuildContext context, types.Message message) {
    final readAt = message.readAt;
    if (readAt == null) return;

    final expiryTime =
        readAt.add(Duration(seconds: message.ephemeralTimeout ?? 0));
    final formatter = intl.DateFormat.yMd().add_Hm();

    final text = 'Message will be deleted in ${formatter.format(expiryTime)}';
    context.showSnackBar(text);
  }

  /// 判断消息是否需要背景色
  /// 媒体消息（图片、视频、语音）通常有自己的背景，不需要额外的气泡背景
  bool _needsBackground(types.Message message) {
    switch (message.type) {
      case types.MessageType.image:
      case types.MessageType.video:
      case types.MessageType.audio:
        return false; // 媒体消息不需要背景
      case types.MessageType.custom:
        // 检查自定义消息的子类型
        final subtype = message.metadata?['subtype'];
        if (subtype == '3') {
          // INVITE_ROOM 消息有自己的背景
          return false;
        }
        return true; // 其他自定义消息（如语音通话）需要背景
      case types.MessageType.text:
      case types.MessageType.system:
      default:
        return true; // 文本和其他消息需要背景
    }
  }
}
