import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/widgets/audio_player/audio_player_controller.dart';
import 'package:flutter_audio_room/core/widgets/audio_player/audio_player_widget.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/message_file_provider.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 语音消息组件
class VoiceMessageWidget extends ConsumerWidget {
  final AudioMessage message;
  final String conversationId;
  final bool isFromMe;

  const VoiceMessageWidget({
    super.key,
    required this.message,
    required this.conversationId,
    required this.isFromMe,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final fileMetadataState =
        ref.watch(messageFileProvider((conversationId, message)));

    // 计算语音消息的宽度，基于时长
    final duration = message.duration;
    final minWidth = 120.w;
    final maxWidth = MediaQuery.of(context).size.width * 0.6;

    // 根据时长计算宽度（每秒增加一定宽度）
    double voiceWidth = minWidth;
    if (duration.inSeconds > 0) {
      voiceWidth = minWidth + (duration.inSeconds * 8.w);
      voiceWidth = voiceWidth.clamp(minWidth, maxWidth);
    }

    return Container(
      constraints: BoxConstraints(
        minWidth: minWidth,
        maxWidth: maxWidth,
      ),
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: isFromMe
            ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
            : Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 播放/暂停按钮
          Container(
            width: 32.w,
            height: 32.w,
            decoration: BoxDecoration(
              color: isFromMe
                  ? Theme.of(context).primaryColor
                  : Theme.of(context).colorScheme.primary,
              shape: BoxShape.circle,
            ),
            child: Builder(
              builder: (context) {
                if (fileMetadataState.hasError) {
                  return Icon(
                    Icons.error_outline,
                    color: Colors.white,
                    size: 18.sp,
                  );
                } else if (fileMetadataState.isLoading ||
                    !fileMetadataState.hasValue ||
                    fileMetadataState.value?.path == null) {
                  return SizedBox(
                    width: 18.w,
                    height: 18.w,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  );
                } else {
                  return AudioPlayerWidget(
                    audioPath: fileMetadataState.value!.path,
                    sourceType: AudioSourceType.file,
                    id: message.id,
                    showProgress: false,
                    playIcon: Icon(
                      Icons.play_arrow_rounded,
                      color: Colors.white,
                      size: 18.sp,
                    ),
                    pauseIcon: Icon(
                      Icons.pause_rounded,
                      color: Colors.white,
                      size: 18.sp,
                    ),
                    loadingWidget: SizedBox(
                      width: 18.w,
                      height: 18.w,
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    ),
                  );
                }
              },
            ),
          ),

          SizedBox(width: 12.w),

          // 波形或进度指示器
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 简化的波形显示
                Row(
                  children: List.generate(
                    20,
                    (index) => Container(
                      width: 2.w,
                      height: (8 + (index % 3) * 4).h,
                      margin: EdgeInsets.only(right: 1.w),
                      decoration: BoxDecoration(
                        color: isFromMe
                            ? Theme.of(context)
                                .primaryColor
                                .withValues(alpha: 0.6)
                            : Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withValues(alpha: 0.4),
                        borderRadius: BorderRadius.circular(1.r),
                      ),
                    ),
                  ),
                ),

                SizedBox(height: 4.h),

                // 时长文本
                Text(
                  _formatDuration(message.duration),
                  style: TextStyle(
                    color: isFromMe
                        ? Theme.of(context).primaryColor
                        : Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withValues(alpha: 0.7),
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(width: 8.w),

          // 语音图标
          Icon(
            Icons.mic_rounded,
            size: 16.sp,
            color: isFromMe
                ? Theme.of(context).primaryColor.withValues(alpha: 0.7)
                : Theme.of(context)
                    .colorScheme
                    .onSurface
                    .withValues(alpha: 0.5),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '$minutes:${seconds.toString().padLeft(2, '0')}';
    }
  }
}
