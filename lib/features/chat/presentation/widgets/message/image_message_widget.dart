import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/widgets/image_preview/hero_image_widget.dart';
import 'package:flutter_audio_room/core/widgets/image_preview/image_preview_widget.dart';
import 'package:flutter_audio_room/core/widgets/image_widget.dart';
import 'package:flutter_audio_room/features/chat/domain/extension/message_metadata_extension.dart';
import 'package:flutter_audio_room/features/chat/domain/models/message_extend_model.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/message_file_provider.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 图片消息组件
class ImageMessageWidget extends ConsumerWidget {
  final ImageMessage message;
  final String conversationId;

  const ImageMessageWidget({
    super.key,
    required this.message,
    required this.conversationId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final fileMetadataState =
        ref.watch(messageFileProvider((conversationId, message)));

    // 计算图片尺寸，参考Telegram的设计
    final screenWidth = MediaQuery.of(context).size.width;
    final maxImageWidth = screenWidth * 0.6; // 最大宽度为屏幕的60%
    final minImageWidth = screenWidth * 0.3; // 最小宽度为屏幕的30%

    // 根据图片比例计算合适的尺寸
    double imageWidth = maxImageWidth;
    double imageHeight = maxImageWidth;

    // 如果有图片尺寸信息，使用实际比例
    if (message.width != null && message.height != null) {
      final aspectRatio = message.width! / message.height!;
      if (aspectRatio > 1) {
        // 横图
        imageWidth = maxImageWidth;
        imageHeight = imageWidth / aspectRatio;
        if (imageHeight < 120.h) imageHeight = 120.h;
      } else {
        // 竖图或正方形
        imageHeight = maxImageWidth;
        imageWidth = imageHeight * aspectRatio;
        if (imageWidth < minImageWidth) {
          imageWidth = minImageWidth;
          imageHeight = imageWidth / aspectRatio;
        }
      }
    } else {
      // 默认正方形
      imageWidth = 200.w;
      imageHeight = 200.w;
    }

    return SizedBox(
      width: imageWidth,
      height: imageHeight,
      child: Stack(
          children: [
            // 根据状态显示不同内容
            Positioned.fill(
              child: Builder(
                builder: (context) {
                  // 处理错误状态
                  if (fileMetadataState.hasError) {
                    return Container(
                      color: Theme.of(context).colorScheme.errorContainer,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.broken_image_outlined,
                            size: 32.sp,
                            color: Theme.of(context).colorScheme.error,
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            'Failed to load',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Theme.of(context).colorScheme.error,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  // 处理加载状态
                  if (fileMetadataState.isLoading ||
                      !fileMetadataState.hasValue) {
                    return Container(
                      color:
                          Theme.of(context).colorScheme.surfaceContainerHighest,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              'Loading...',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withValues(alpha: 0.6),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }

                  // 处理成功状态
                  final metadata = fileMetadataState.value;
                  if (metadata == null) {
                    return Container(
                      color:
                          Theme.of(context).colorScheme.surfaceContainerHighest,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.image_not_supported_outlined,
                            size: 32.sp,
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withValues(alpha: 0.5),
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            'Image not found',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurface
                                  .withValues(alpha: 0.5),
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return GestureDetector(
                    onTap: () {
                      // 添加点击预览功能
                      ImagePreviewModal.show(
                        context: context,
                        source: ImageSource.file,
                        imagePath: metadata.path,
                        tag: message.id,
                      );
                    },
                    child: HeroImageWidget.file(
                      path: metadata.path,
                      tag: message.id,
                      fit: BoxFit.cover,
                    ),
                  );
                },
              ),
            ),

            // 显示文件下载状态覆盖层
            if (message.fileStatus != FileStatus.downloaded)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const CircularProgressIndicator(
                          strokeWidth: 3,
                          color: Colors.white,
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          'Downloading...',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    )
  }
}
