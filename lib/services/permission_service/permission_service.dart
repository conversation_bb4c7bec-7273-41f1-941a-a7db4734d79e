import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionService {
  /// Check and request microphone permission
  /// Returns true if permission is granted, false otherwise
  Future<ResultWithData<PermissionStatus>>
      checkAndRequestMicrophonePermission() async {
    return _checkAndRequestPermission(
      Permission.microphone,
    );
  }

  /// Check if microphone permission is granted
  Future<VoidResult> isMicrophonePermissionGranted() async {
    try {
      final status = await Permission.microphone.status;
      if (status.isGranted) {
        return const Right(null);
      } else {
        return const Left(AppException(
          message: 'Microphone permission is not granted',
          identifier: 'PERMISSION_DENIED',
          statusCode: 403,
        ));
      }
    } catch (e) {
      return Left(AppException(
        message: e.toString(),
        identifier: 'PERMISSION_ERROR',
        statusCode: 400,
      ));
    }
  }

  /// Open app settings
  Future<VoidResult> openSettings() async {
    try {
      final result = await openAppSettings();
      if (result) {
        return const Right(null);
      } else {
        return const Left(AppException(
          message: 'Failed to open settings',
          identifier: 'SETTINGS_ERROR',
          statusCode: 500,
        ));
      }
    } catch (e) {
      return Left(AppException(
        message: e.toString(),
        identifier: 'PERMISSION_ERROR',
        statusCode: 400,
      ));
    }
  }

  /// Generic method to check and request any permission
  /// [permission] The permission to check and request
  /// [permissionName] The name of the permission for error messages
  Future<ResultWithData<PermissionStatus>> _checkAndRequestPermission(
    Permission permission,
  ) async {
    try {
      LogUtils.d('检查权限: ${permission.toString()}', tag: 'PermissionService');
      final status = await permission.status;
      LogUtils.d('当前权限状态: $status', tag: 'PermissionService');

      // If permission is already granted, return the status
      if (status.isGranted) {
        LogUtils.d('权限已授予', tag: 'PermissionService');
        return Right(status);
      }

      // If permission is restricted, return restricted status
      if (status.isRestricted) {
        LogUtils.w('权限被限制', tag: 'PermissionService');
        return Left(AppException(
          message: '${permission.toString()} permission is restricted',
          identifier: 'PERMISSION_RESTRICTED',
          statusCode: 403,
        ));
      }

      // If permission is permanently denied, open settings
      if (status.isPermanentlyDenied) {
        LogUtils.w('权限被永久拒绝', tag: 'PermissionService');
        return Left(AppException(
          message:
              '${permission.toString()} permission is permanently denied. Please enable it in Settings',
          identifier: 'PERMISSION_PERMANENTLY_DENIED',
          statusCode: 403,
        ));
      }

      // If permission is denied, try to request it
      if (status.isDenied) {
        LogUtils.d('权限被拒绝，尝试请求权限', tag: 'PermissionService');
        final result = await permission.request();
        LogUtils.d('权限请求结果: $result', tag: 'PermissionService');

        // Handle request result
        if (result.isGranted) {
          LogUtils.d('权限请求成功', tag: 'PermissionService');
          return Right(result);
        } else if (result.isPermanentlyDenied) {
          LogUtils.w('权限被永久拒绝', tag: 'PermissionService');
          return Left(AppException(
            message:
                '${permission.toString()} permission is permanently denied. Please enable it in Settings',
            identifier: 'PERMISSION_PERMANENTLY_DENIED',
            statusCode: 403,
          ));
        } else {
          LogUtils.w('权限请求被拒绝', tag: 'PermissionService');
          return Left(AppException(
            message: '${permission.toString()} permission is denied',
            identifier: 'PERMISSION_DENIED',
            statusCode: 403,
          ));
        }
      }

      // Handle unknown status
      LogUtils.e('未知权限状态: $status', tag: 'PermissionService');
      return const Left(AppException(
        message: 'Unknown permission status',
        identifier: 'PERMISSION_UNKNOWN',
        statusCode: 400,
      ));
    } catch (e) {
      LogUtils.e('权限检查异常: $e', tag: 'PermissionService');
      return Left(AppException(
        message: e.toString(),
        identifier: 'PERMISSION_ERROR',
        statusCode: 500,
      ));
    }
  }
}
