import 'dart:io';

import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:permission_handler/permission_handler.dart';

/// 权限调试工具类
/// 用于诊断权限相关问题，特别是在模拟器上
class PermissionDebugUtils {
  static const String _tag = 'PermissionDebugUtils';

  /// 检查设备和权限状态
  static Future<void> debugPermissionStatus() async {
    LogUtils.d('=== 权限调试信息开始 ===', tag: _tag);
    
    // 设备信息
    await _logDeviceInfo();
    
    // 权限状态
    await _logPermissionStatus();
    
    LogUtils.d('=== 权限调试信息结束 ===', tag: _tag);
  }

  /// 记录设备信息
  static Future<void> _logDeviceInfo() async {
    LogUtils.d('设备平台: ${Platform.operatingSystem}', tag: _tag);
    LogUtils.d('设备版本: ${Platform.operatingSystemVersion}', tag: _tag);
    
    // 检查是否为模拟器
    bool isEmulator = false;
    try {
      // 在Android模拟器上，这些属性通常包含"emulator"或"simulator"
      if (Platform.isAndroid) {
        // 可以通过检查一些系统属性来判断是否为模拟器
        LogUtils.d('Android设备检测', tag: _tag);
      } else if (Platform.isIOS) {
        // iOS模拟器检测
        LogUtils.d('iOS设备检测', tag: _tag);
      }
    } catch (e) {
      LogUtils.e('设备信息检测失败: $e', tag: _tag);
    }
    
    LogUtils.d('疑似模拟器: $isEmulator', tag: _tag);
  }

  /// 记录权限状态
  static Future<void> _logPermissionStatus() async {
    final permissions = [
      Permission.microphone,
      Permission.camera,
      Permission.storage,
      Permission.photos,
      Permission.notification,
    ];

    for (final permission in permissions) {
      try {
        final status = await permission.status;
        LogUtils.d('${permission.toString()}: $status', tag: _tag);
        
        // 详细状态信息
        LogUtils.d('  - isGranted: ${status.isGranted}', tag: _tag);
        LogUtils.d('  - isDenied: ${status.isDenied}', tag: _tag);
        LogUtils.d('  - isPermanentlyDenied: ${status.isPermanentlyDenied}', tag: _tag);
        LogUtils.d('  - isRestricted: ${status.isRestricted}', tag: _tag);
        LogUtils.d('  - isLimited: ${status.isLimited}', tag: _tag);
      } catch (e) {
        LogUtils.e('检查权限 ${permission.toString()} 失败: $e', tag: _tag);
      }
    }
  }

  /// 测试麦克风权限请求
  static Future<void> testMicrophonePermission() async {
    LogUtils.d('=== 麦克风权限测试开始 ===', tag: _tag);
    
    try {
      // 1. 检查当前状态
      final currentStatus = await Permission.microphone.status;
      LogUtils.d('当前麦克风权限状态: $currentStatus', tag: _tag);
      
      // 2. 如果未授予，尝试请求
      if (!currentStatus.isGranted) {
        LogUtils.d('权限未授予，开始请求...', tag: _tag);
        final requestResult = await Permission.microphone.request();
        LogUtils.d('权限请求结果: $requestResult', tag: _tag);
        
        // 3. 再次检查状态
        final finalStatus = await Permission.microphone.status;
        LogUtils.d('最终麦克风权限状态: $finalStatus', tag: _tag);
      }
      
      // 4. 检查是否可以打开设置
      final canOpenSettings = await openAppSettings();
      LogUtils.d('可以打开应用设置: $canOpenSettings', tag: _tag);
      
    } catch (e) {
      LogUtils.e('麦克风权限测试失败: $e', tag: _tag);
    }
    
    LogUtils.d('=== 麦克风权限测试结束 ===', tag: _tag);
  }

  /// 模拟器特定的权限建议
  static void logEmulatorPermissionTips() {
    LogUtils.d('=== 模拟器权限问题解决建议 ===', tag: _tag);
    LogUtils.d('1. Android模拟器:', tag: _tag);
    LogUtils.d('   - 确保AVD配置中启用了麦克风', tag: _tag);
    LogUtils.d('   - 在模拟器设置中检查音频输入设备', tag: _tag);
    LogUtils.d('   - 尝试冷启动模拟器', tag: _tag);
    LogUtils.d('   - 检查主机系统的麦克风权限', tag: _tag);
    
    LogUtils.d('2. iOS模拟器:', tag: _tag);
    LogUtils.d('   - iOS模拟器通常不支持麦克风录音', tag: _tag);
    LogUtils.d('   - 建议使用真机测试录音功能', tag: _tag);
    
    LogUtils.d('3. 通用建议:', tag: _tag);
    LogUtils.d('   - 检查AndroidManifest.xml中的权限声明', tag: _tag);
    LogUtils.d('   - 检查Info.plist中的权限描述', tag: _tag);
    LogUtils.d('   - 尝试手动在设置中授予权限', tag: _tag);
    LogUtils.d('   - 重启应用后再次测试', tag: _tag);
    LogUtils.d('================================', tag: _tag);
  }
}
