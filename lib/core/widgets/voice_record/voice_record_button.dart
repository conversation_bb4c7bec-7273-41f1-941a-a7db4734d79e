import 'dart:async';

import 'package:audio_session/audio_session.dart';
import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/core/utils/permission_debug_utils.dart';
import 'package:flutter_audio_room/services/file_service/i_file_service.dart';
import 'package:flutter_audio_room/services/permission_service/permission_service.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:permission_handler/permission_handler.dart';

class VoiceRecordButton extends StatefulWidget {
  final Function(String path, Duration duration) onRecordComplete;
  final Widget? child;
  final double? width;
  final double? height;
  final Color? backgroundColor;
  final Color? progressColor;
  final double? progressStrokeWidth;

  const VoiceRecordButton({
    super.key,
    required this.onRecordComplete,
    this.child,
    this.width,
    this.height,
    this.backgroundColor,
    this.progressColor,
    this.progressStrokeWidth,
  });

  @override
  State<VoiceRecordButton> createState() => _VoiceRecordButtonState();
}

class _VoiceRecordButtonState extends State<VoiceRecordButton> {
  final _recorder = FlutterSoundRecorder();
  final _permissionService = PermissionService();
  bool _isRecording = false;
  bool _isInitialized = false;
  Timer? _timer;
  Duration _recordingDuration = Duration.zero;
  String? _recordingPath;
  final Codec _codec = Codec.pcm16WAV;

  @override
  void initState() {
    super.initState();
    _initializeRecorder();
    // 在调试模式下输出权限调试信息
    _debugPermissions();
  }

  /// 调试权限状态
  Future<void> _debugPermissions() async {
    // 只在调试模式下执行
    assert(() {
      PermissionDebugUtils.debugPermissionStatus();
      PermissionDebugUtils.logEmulatorPermissionTips();
      return true;
    }());
  }

  Future<void> _initializeRecorder() async {
    try {
      // 先检查权限，再初始化录音器
      final result =
          await _permissionService.checkAndRequestMicrophonePermission();

      await result.fold(
        (error) async {
          LogUtils.e('麦克风权限检查失败: ${error.message}', tag: 'VoiceRecordButton');
          _handlePermissionError();
          return;
        },
        (status) async {
          LogUtils.d('麦克风权限状态: $status', tag: 'VoiceRecordButton');
          try {
            await _recorder.openRecorder();
            _isInitialized = true;
            LogUtils.d('录音器初始化成功', tag: 'VoiceRecordButton');
          } catch (e) {
            LogUtils.e('录音器初始化失败: $e', tag: 'VoiceRecordButton');
            _isInitialized = false;
          }
        },
      );
    } catch (e) {
      LogUtils.e('初始化录音器异常: $e', tag: 'VoiceRecordButton');
      _isInitialized = false;
    }
  }

  Future<void> _handlePermissionError() async {
    final result = await context.showOkCancelAlertDialog(
      title: 'Microphone Permission Required',
      content: 'Please enable microphone permission in settings',
    );

    if (result == true) {
      openAppSettings();
    }
  }

  Future<void> _configureAudioSession() async {
    try {
      final session = await AudioSession.instance;
      await session.configure(
        AudioSessionConfiguration(
          avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
          avAudioSessionCategoryOptions:
              AVAudioSessionCategoryOptions.allowBluetooth |
                  AVAudioSessionCategoryOptions.defaultToSpeaker,
          avAudioSessionMode: AVAudioSessionMode.spokenAudio,
          avAudioSessionRouteSharingPolicy:
              AVAudioSessionRouteSharingPolicy.defaultPolicy,
          avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
          androidAudioAttributes: const AndroidAudioAttributes(
            contentType: AndroidAudioContentType.speech,
            flags: AndroidAudioFlags.none,
            usage: AndroidAudioUsage.voiceCommunication,
          ),
          androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
          androidWillPauseWhenDucked: true,
        ),
      );
    } catch (e) {
      LogUtils.e('配置音频会话失败: $e', tag: 'VoiceRecordButton');
    }
  }

  Future<void> _startRecording() async {
    LogUtils.d('开始录音请求', tag: 'VoiceRecordButton');

    // 重新检查权限状态（不使用缓存）
    final permissionResult =
        await _permissionService.checkAndRequestMicrophonePermission();
    if (permissionResult.isLeft()) {
      permissionResult.fold(
        (error) =>
            LogUtils.e('权限检查失败: ${error.message}', tag: 'VoiceRecordButton'),
        (_) => null,
      );
      _handlePermissionError();
      return;
    }

    // 如果录音器未初始化，先初始化
    if (!_isInitialized) {
      LogUtils.d('录音器未初始化，开始初始化', tag: 'VoiceRecordButton');
      await _initializeRecorder();
    }

    if (!_isInitialized) {
      LogUtils.e('录音器初始化失败', tag: 'VoiceRecordButton');
      _handlePermissionError();
      return;
    }

    // 配置音频会话
    await _configureAudioSession();

    // 生成录音文件路径
    _recordingPath =
        '${getIt<IFileService>().tempDir.path}/audio_${DateTime.now().millisecondsSinceEpoch}${ext[_codec.index]}';

    LogUtils.d('开始录音到文件: $_recordingPath', tag: 'VoiceRecordButton');

    try {
      await _recorder.startRecorder(
        toFile: _recordingPath,
        codec: _codec,
        audioSource: AudioSource.defaultSource,
        sampleRate: 44100,
      );

      LogUtils.d('录音启动成功', tag: 'VoiceRecordButton');

      setState(() {
        _isRecording = true;
        _recordingDuration = Duration.zero;
      });

      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _recordingDuration += const Duration(seconds: 1);
        });
      });
    } catch (e) {
      LogUtils.e('录音启动失败: $e', tag: 'VoiceRecordButton');
      setState(() {
        _isRecording = false;
      });

      // 如果是权限相关错误，显示权限对话框
      if (e.toString().contains('permission') ||
          e.toString().contains('Permission')) {
        _handlePermissionError();
      }
    }
  }

  Future<void> _stopRecording() async {
    if (!_isRecording) return;

    _timer?.cancel();
    try {
      await _recorder.stopRecorder();

      setState(() {
        _isRecording = false;
      });

      if (_recordingPath != null) {
        widget.onRecordComplete(_recordingPath!, _recordingDuration);
        _recordingPath = null;
      }
    } catch (e) {
      LogUtils.d('录音停止失败: $e', tag: 'VoiceRecordButton');
      _isRecording = false;
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    _recorder.closeRecorder();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onLongPressStart: (_) => _startRecording(),
      onLongPressEnd: (_) => _stopRecording(),
      child: Container(
        width: widget.width ?? double.infinity,
        height: widget.height ?? 48,
        decoration: BoxDecoration(
          color: widget.backgroundColor ?? Colors.blue,
          borderRadius: BorderRadius.circular(24),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            if (_isRecording)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(24),
                  ),
                ),
              ),
            if (_isRecording)
              Positioned(
                left: 16,
                child: Text(
                  '${_recordingDuration.inSeconds}s',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            if (_isRecording)
              Positioned(
                right: 16,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.mic,
                        color: Colors.white,
                        size: 16,
                      ),
                      SizedBox(width: 4),
                      Text(
                        '录音中...',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            if (!_isRecording)
              widget.child ??
                  const Icon(
                    Icons.mic_none,
                    color: Colors.white,
                  ),
          ],
        ),
      ),
    );
  }
}
